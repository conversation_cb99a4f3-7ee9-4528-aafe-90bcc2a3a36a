package com.ruoyi.ocr.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.ocr.service.OcrService;
import lombok.SneakyThrows;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/8/15 15:03
 * @description
 */

@RestController
@RequestMapping("/infra/ocr")
public class OcrController extends BaseController {
    @Resource
    private OcrService ocrService;


    @GetMapping()
    public String ocr() {
        String imgPath = "E:/work/secdriver/dataSecurityAssessment-V3/backend-java/ruoyi-infra/src/main/resources/images/lc2.png";
        String content = ocrService.getContent(imgPath);
        return content;
    }

    @GetMapping("/match")
    public String match(@RequestParam String signature, @RequestParam String imgPath) {
        // String imgPath = "E:/work/secdriver/dataSecurityAssessment-V3/backend-java/ruoyi-infra/src/main/resources/images/lc1.png";
        Boolean flag = ocrService.isMatchSignature(imgPath, signature);
        return flag ? "匹配成功" : "匹配失败";
    }

    @SneakyThrows
    private static String getResourcePath(String path) {
        return Thread.currentThread().getContextClassLoader().getResource(path).getPath();
    }
}
