package com.ruoyi.ocr.service;

import com.benjaminwan.ocrlibrary.OcrResult;
import io.github.mymonstercat.Model;
import io.github.mymonstercat.ocr.InferenceEngine;
import io.github.mymonstercat.ocr.config.ParamConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/8/15 16:17
 * @description
 */
@Slf4j
@Service("ocrService")
public class OcrService {
    public String getContent(String imgPath) {
        ParamConfig paramConfig = ParamConfig.getDefaultConfig();
        paramConfig.setDoAngle(true);
        paramConfig.setMostAngle(true);
        InferenceEngine engine = InferenceEngine.getInstance(Model.ONNX_PPOCR_V3);

        OcrResult ocrResult = engine.runOcr(imgPath, paramConfig);
        return ocrResult.getStrRes();
    }

    public Boolean isMatchSignature(String imgPath, String signature) {
        ParamConfig paramConfig = ParamConfig.getDefaultConfig();
        paramConfig.setDoAngle(true);
        paramConfig.setMostAngle(true);
        InferenceEngine engine = InferenceEngine.getInstance(Model.ONNX_PPOCR_V3);

        OcrResult ocrResult = engine.runOcr(imgPath, paramConfig);
        String res = ocrResult.getStrRes();
        res = res.replaceAll("\\s", "");
        boolean match = false;
        if (res.equals(signature)) {
            match = true;
        }
        log.info("OCR识别结果: {}, 签名结果：{}, isMatch：{}", res, signature, match);
        return match;
    }
}
